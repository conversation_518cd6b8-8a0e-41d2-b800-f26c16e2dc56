// Imports
import { Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { DataCodesModule } from 'src/neighbours/data-codes/data.codes.module';
import { UtilsModule } from 'src/utils/utils.module';
import { PgModule } from 'src/database/pg/pg.module';

@Module({
  controllers: [PaymentController],
  imports: [DataCodesModule, UtilsModule, PgModule],
  providers: [PaymentService],
})
export class PaymentModule {}
