// Imports
import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { TransactionEntity } from 'src/database/pg/entities/transaction.entity';
import { DataCodesService } from 'src/neighbours/data-codes/data.codes.service';
import { DateService } from 'src/utils/date.service';
import { PgService } from 'src/database/pg/pg.service';
import { raiseNotFound } from 'src/config/error';

@Injectable()
export class PaymentService {
  constructor(
    private readonly dataCodes: DataCodesService,
    private readonly dateService: DateService,
    private readonly pg: PgService,
  ) {}

  async forecast() {
    const response = await this.dataCodes.paymentForecast();
    const currentDate = this.dateService.getGlobalDate(new Date());

    const currentDay = currentDate.getDate();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    const data = [];

    for (const [index, item] of response.entries()) {
      const startDay = index * 5 + 1;
      const endDay = Math.min(startDay + 4, 31);

      if (startDay > currentDay) {
        data.push({ ...item, 'Amount Received': '₹0.00 Cr' });
        continue;
      }

      const adjustedEndDay =
        currentDay >= startDay && currentDay <= endDay ? currentDay : endDay;

      const startDate = this.dateService
        .getGlobalDate(new Date(currentYear, currentMonth, startDay))
        .toJSON();

      const endDate = this.dateService
        .getGlobalDate(new Date(currentYear, currentMonth, adjustedEndDay))
        .toJSON();

      const amountRec = await this.funAmountReceived({
        startDate: startDate,
        endDate: endDate,
      });

      data.push({ ...item, 'Amount Received': amountRec });
    }

    return { success: true, data };
  }

  async asOnDateRecovery() {
    const response = await this.dataCodes.asOnDateRecovery();
    return { success: true, data: response };
  }

  //#region Amount Received
  async funAmountReceived(reqData) {
    const { startDate, endDate } = reqData;
    const start = this.dateService.getGlobalDate(startDate).toJSON();
    const end = this.dateService.getGlobalDate(endDate).toJSON();

    const transactions = await this.pg.findAll(TransactionEntity, {
      attributes: ['id', 'paidAmount', 'completionDate'],
      where: {
        status: 'COMPLETED',
        type: { [Op.ne]: 'REFUND' },
        completionDate: { [Op.gte]: start, [Op.lte]: end },
      },
    });

    if (!transactions) raiseNotFound();

    let totalAmount = 0;
    for (let i = 0; i < transactions?.length; i++) {
      totalAmount += transactions[i]?.paidAmount ?? 0;
    }

    const amountInCr = `₹${(totalAmount / 10000000).toFixed(2)} Cr`;

    return amountInCr;
  }
  //#endregion
}
